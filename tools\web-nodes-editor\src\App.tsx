import React, {
  useState,
  useRef,
  useEffect,
  KeyboardEvent as ReactKeyboardEvent,
  MouseEvent as ReactMouseEvent,
  useMemo,
  useCallback,
} from "react";
import { WebTile } from "./models/WebTile";
import WebData from "C:/Users/<USER>/Desktop/GoldfarmData/fridabot/src/data/WebData.json";
import { EditNodeDialog } from "./components/EditNodeDialog";
import { TiledImageRenderer } from './components/TiledImageRenderer'; // Import the new component
import { MapImgsRenderer } from "./components/FastImgRenderer";
import { imageDimensions, mapSize } from "./types/mapDimensions";

const App: React.FC = () => {
  const [webTiles, setWebTiles] = useState<WebTile[]>(WebData);
  const [selected, setSelected] = useState<WebTile | null>(null);
  const [scale, setScale] = useState(1);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentPlane, setCurrentPlane] = useState(0);

  const containerRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<SVGSVGElement>(null);
  const [mouseDown, setMouseDown] = useState(false);
  const [lastMousePosition, setLastMousePosition] = useState({ x: 0, y: 0 });
  const [hoveredTile, setHoveredTile] = useState<WebTile | null>(null);
  const [worldPosition, setWorldPosition] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });

  // Memoize worldToScreen and screenToWorld functions to avoid recreation on each render
  const worldToScreen = useCallback(
    (x: number, y: number): { x: number; y: number } => {
      return {
        x: (x - mapSize.minX) * 4 ,
        y: (imageDimensions.height - (y - mapSize.minY) * 4) ,
      };
    },
    [ imageDimensions.height]
  );

  const screenToWorld = useCallback(
    (x: number, y: number): { x: number; y: number } => {
      return {
        x: Math.floor(mapSize.minX + x / scale / 4),
        y: Math.floor(mapSize.minY + (imageDimensions.height - y / scale) / 4),
      };
    },
    [scale, imageDimensions.height]
  );

  // Smoothed zoom handler
  const handleWheel = useCallback(
    (e: React.WheelEvent<HTMLDivElement>) => {
      e.preventDefault();

      if (containerRef.current) {
        const container = containerRef.current;
        const rect = container.getBoundingClientRect();

        // Get mouse position relative to the container
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // Calculate zoom delta with smoother scaling
        const delta = -e.deltaY;
        const zoomFactor = 1.25;
        const zoomDelta = delta > 0 ? zoomFactor : 1 / zoomFactor;

        // Calculate new scale with bounds
        const newScale = Math.min(Math.max(scale * zoomDelta, 0.1), 664);

        // Early exit if scale hasn't changed significantly
        if (Math.abs(newScale - scale) < 0.001) return;

        // Calculate the position before zoom
        const beforeZoomX = container.scrollLeft + mouseX;
        const beforeZoomY = container.scrollTop + mouseY;

        // Calculate the scaling factor
        const scaleFactor = newScale / scale;

        // Set the new scale
        setScale(newScale);

        // Calculate and set the new scroll position to maintain the mouse position
        // This needs to happen after the scale has been updated in the DOM
        setTimeout(() => {
          if (containerRef.current) {
            // Calculate the new scroll position to keep the mouse point fixed
            const newScrollX = beforeZoomX * scaleFactor - mouseX;
            const newScrollY = beforeZoomY * scaleFactor - mouseY;

            // Update the scroll position
            containerRef.current.scrollLeft = newScrollX;
            containerRef.current.scrollTop = newScrollY;
          }
        }, 0);
      }
    },
    [scale]
  );

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener("wheel", handleWheel as any, {
      passive: false,
    });
    return () => {
      container.removeEventListener("wheel", handleWheel as any);
    };
  }, [handleWheel]);

  const handleMouseDown = useCallback((e: ReactMouseEvent<HTMLDivElement>) => {
    if (e.button === 1) {
      e.preventDefault();
      setMouseDown(true);
      setLastMousePosition({ x: e.clientX, y: e.clientY });
    }
  }, []);

  const handleMouseMove = useCallback(
    (e: ReactMouseEvent<HTMLDivElement>) => {


      if (mouseDown && containerRef.current) {
        const dx = e.clientX - lastMousePosition.x;
        const dy = e.clientY - lastMousePosition.y;

        containerRef.current.scrollLeft -= dx;
        containerRef.current.scrollTop -= dy;

        setLastMousePosition({ x: e.clientX, y: e.clientY });
      }

      if (overlayRef.current) {
        const rect = overlayRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const worldPos = screenToWorld(x, y);
        setWorldPosition(worldPos);
      }
    },
    [mouseDown, lastMousePosition, screenToWorld]
  );

  const handleMouseUp = useCallback(
    (e: ReactMouseEvent<HTMLDivElement>) => {

      console.log("Hovered tile: ", hoveredTile);
      if(hoveredTile) return

      if (e.button === 1) {
        setMouseDown(false);
        return;
      }

      // Only proceed if click was on the map-content div
      const target = e.target as HTMLElement;
      if (!target.closest(".map-content")) {
        return;
      }

      if (!overlayRef.current) return;

      const rect = overlayRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const worldPos = screenToWorld(x, y);

      const clickedTile = webTiles.find((tile) => {
        const tileScreen = worldToScreen(tile.x, tile.y);
        return (
          Math.abs(tileScreen.x - x) < 4  &&
          Math.abs(tileScreen.y - y) < 4
        );
      });

      if (e.button === 2 && selected && clickedTile) {
        connectTiles(clickedTile, selected);
      } else if (!clickedTile && e.button === 0) {
        addNewTile(worldPos, selected);
      } else if (clickedTile && e.button === 0) {
        setSelected(clickedTile);
      }
    },
    [webTiles, selected, hoveredTile,  screenToWorld, worldToScreen]
  );

  const connectTiles = useCallback((tile1: WebTile, tile2: WebTile) => {
    setWebTiles((prevTiles) => {
      return prevTiles.map((tile) => {
        if (tile.id === tile1.id) {
          return {
            ...tile,
            destinationNodeIds: [
              ...new Set([...tile.destinationNodeIds, tile2.id]),
            ],
          };
        }
        if (tile.id === tile2.id) {
          return {
            ...tile,
            destinationNodeIds: [
              ...new Set([...tile.destinationNodeIds, tile1.id]),
            ],
          };
        }
        return tile;
      });
    });
  }, []);

  const addNewTile = useCallback(
    (position: { x: number; y: number }, parentTile: WebTile | null) => {
      const newTile: WebTile = {
        id: Math.floor(Math.random() * 1000000000),
        type: "REGULAR",
        tileType: "WORLD",
        objectName: "",
        action: "",
        climbUpAction: "",
        climbDownAction: "",
        npcName: "",
        dialogues: [],
        x: position.x,
        y: position.y,
        z: 0,
        destinationNodeIds: parentTile ? [parentTile.id] : [],
      };

      setWebTiles((prevTiles) => {
        const updatedTiles = [...prevTiles];
        if (parentTile) {
          const parentIndex = updatedTiles.findIndex(
            (t) => t.id === parentTile.id
          );
          if (parentIndex !== -1) {
            updatedTiles[parentIndex] = {
              ...parentTile,
              destinationNodeIds: [
                ...parentTile.destinationNodeIds,
                newTile.id,
              ],
            };
          }
        }
        updatedTiles.push(newTile);
        return updatedTiles;
      });
      setSelected(newTile);
    },
    []
  );

  const handleEditNode = (editedTile: WebTile) => {
    setWebTiles((prevTiles) =>
      prevTiles.map((tile) => (tile === selected ? editedTile : tile))
    );
    setSelected(editedTile);
  };

  const handleKeyDown = useCallback(
    (event: ReactKeyboardEvent<HTMLDivElement>) => {
      if (event.key === "Delete" && selected) {
        setWebTiles((prevTiles) =>
          prevTiles
            .filter((tile) => tile.id !== selected.id)
            .map((tile) => ({
              ...tile,
              destinationNodeIds: tile.destinationNodeIds.filter(
                (id) => id !== selected.id
              ),
            }))
        );
        setSelected(null);
      }
      if (event.key.toLowerCase() === "e" && selected) {
        setShowEditDialog(true);
      }
    },
    [selected]
  );

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown as any);
    return () => window.removeEventListener("keydown", handleKeyDown as any);
  }, [handleKeyDown]);

  // Use a ref to track if the initial centering has been done
  const hasInitialized = useRef(false);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    if (!hasInitialized.current) {
      const x = (3212 - 1152) * 4 ;
      const y = (imageDimensions.height - (3427 - 1215) * 4);
      container.scrollLeft = x - container.clientWidth / 2;
      container.scrollTop = y - container.clientHeight / 2;
      hasInitialized.current = true;
    }
  }, []);

  const renderedLines = useMemo(() => {
    const lines = [];
    for (const tile of webTiles.filter(t => t.z == currentPlane)) {
      for (const destId of tile.destinationNodeIds) {
        const dest = webTiles.find((t) => t.id === destId);
        if (dest) {
          const start = worldToScreen(tile.x, tile.y);
          const end = worldToScreen(dest.x, dest.y);
          lines.push(
            <line
              key={`${tile.id}-${destId}`}
              x1={start.x + 2 }
              y1={start.y + 2 }
              x2={end.x + 2 }
              y2={end.y + 2 }
              stroke="#33b5e5"
              strokeWidth={1 }
            />
          );
        }
      }
    }
    return lines;
  }, [webTiles, currentPlane, worldToScreen, scale]);

  const renderedNodes = useMemo(() => {
    return webTiles.filter(t => t.z == currentPlane).map((tile) => {
      const point = worldToScreen(tile.x, tile.y);


      return (
        <rect
          key={tile.id}
          x={point.x}
          y={point.y}
          width={4}
          height={4 }
          fill={
            tile === selected
              ? "#00ff00"
              : tile.type === "OBJECT"
              ? "orange"
              : "red"
          }
          style={{ pointerEvents: "auto", cursor: "pointer" }}
          onClick={(e) => {
            e.stopPropagation();
            setSelected(tile);
          }}
          onDoubleClick={(e) => {
            e.stopPropagation();
            setShowEditDialog(true);
          }}
          onContextMenu={(e) => {
            e.preventDefault();
            if (selected && tile.id !== selected.id) {
              connectTiles(tile, selected);
            }
          }}
          onMouseEnter={() => setHoveredTile(tile)}
          onMouseLeave={() => setHoveredTile(null)}
        />
      );
    });
  }, [webTiles, currentPlane, selected, scale, worldToScreen, connectTiles]);



  return (
    <>
      <div
        ref={containerRef}
        className="map-container"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onContextMenu={(e) => e.preventDefault()}
        style={{
          width: "100vw",
          height: "100vh",
          overflow: "auto",
          position: "relative",
          backgroundColor: "#000",
          cursor: mouseDown ? "grabbing" : "crosshair",
        }}
      >
        <div
          style={{
            position: "fixed",
            top: "10px",
            left: "10px",
            padding: "8px",
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            color: "#fff",
            borderRadius: "4px",
            zIndex: 1000,
          }}
        >
          <strong>World Position:</strong>
          <br />
          X: {worldPosition.x}, Y: {worldPosition.y}, Z: {currentPlane}
          <br />

          <div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setCurrentPlane(old => old + 1);
            }}
            style={{
              marginTop: "8px",
              padding: "4px 8px",
              backgroundColor: "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
           +
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation();
              setCurrentPlane(old => old - 1);
            }}
            style={{
              marginTop: "8px",
              padding: "4px 8px",
              backgroundColor: "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
           -
          </button>

          </div>

          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowExportDialog(true);
            }}
            style={{
              marginTop: "8px",
              padding: "4px 8px",
              backgroundColor: "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Export Web Data
          </button>


        </div>

        {selected && (
          <div
            style={{
              position: "fixed",
              top: "10px",
              right: "10px",
              padding: "12px",
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              color: "#fff",
              borderRadius: "4px",
              zIndex: 1000,
              maxWidth: "300px",
            }}
          >
            <h3 style={{ margin: "0 0 8px 0" }}>Selected Tile Info</h3>
            Hovered tile: {hoveredTile?.id}
            <div style={{ fontSize: "14px" }}>
              <p style={{ margin: "4px 0" }}>
                <strong>ID:</strong> {selected.id}
              </p>
              <p style={{ margin: "4px 0" }}>
                <strong>Type:</strong> {selected.type}
              </p>
              <p style={{ margin: "4px 0" }}>
                <strong>Tile Type:</strong> {selected.tileType}
              </p>
              <p style={{ margin: "4px 0" }}>
                <strong>Position:</strong> X: {selected.x}, Y: {selected.y}, Z:{" "}
                {selected.z}
              </p>
              {selected.objectName && (
                <p style={{ margin: "4px 0" }}>
                  <strong>Object Name:</strong> {selected.objectName}
                </p>
              )}
              {selected.action && (
                <p style={{ margin: "4px 0" }}>
                  <strong>Action:</strong> {selected.action}
                </p>
              )}
              {selected.npcName && (
                <p style={{ margin: "4px 0" }}>
                  <strong>NPC Name:</strong> {selected.npcName}
                </p>
              )}
              <p style={{ margin: "4px 0" }}>
                <strong>Connected Nodes:</strong>{" "}
                {selected.destinationNodeIds.join(", ") || "None"}
              </p>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowEditDialog(true);
                }}
                className="mt-4 w-full px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
              >
                Edit Node
              </button>
            </div>
          </div>
        )}

        <div
          className="map-content"
          style={{
            position: "relative",
            transformOrigin: "0 0",
            width: imageDimensions.width,
            height: imageDimensions.height,
            transform: `scale(${scale})`,
          }}
        >
            <MapImgsRenderer z={currentPlane} />
          <svg
            ref={overlayRef}
            className="map-overlay"
            width={imageDimensions.width }
            height={imageDimensions.height }
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              pointerEvents: "none",
              width: imageDimensions.width ,
              height: imageDimensions.height,
            }}
          >
            {renderedLines}
            {renderedNodes}
            { worldPosition && (
              <rect
                x={worldToScreen(worldPosition.x, worldPosition.y).x}
                y={worldToScreen(worldPosition.x, worldPosition.y).y}
                width={4}
                height={4 }
                fill="rgba(0, 123, 255, 0.8)"
                style={{ pointerEvents: "none" }}
              />
            )}
          </svg>
        </div>
      </div>

      {showExportDialog && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 2000,
          }}
        >
          <div

            style={{
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              padding: "20px",
              borderRadius: "8px",
              overflow: "auto",
              width: "600px"
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                marginBottom: "16px",
              }}
            >
              <h2 style={{ margin: 0 }}>Export Web Data</h2>
              <button
                onClick={() => setShowExportDialog(false)}
                style={{
                  backgroundColor: "#ff4444",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "4px 8px",
                  cursor: "pointer",
                }}
              >
                Close
              </button>
            </div>
            <textarea
              value={JSON.stringify(webTiles, null, 2)}
              readOnly
              style={{
                width: "100%",
                minHeight: "400px",
                fontFamily: "monospace",
                padding: "8px",
                borderRadius: "4px",
                border: "1px solid #ccc",
              }}
            />
          </div>
        </div>
      )}
      <EditNodeDialog
        open={showEditDialog}
        tile={selected}
        onClose={() => setShowEditDialog(false)}
        onSave={handleEditNode}
      />
    </>
  );
};

export default App;